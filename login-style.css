/* login-style.css - تنسيقات صفحة تسجيل الدخول */

:root {
    --primary-color: #0056b3;
    --secondary-color: #28a745;
    --accent-color: #ffc107;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --danger-color: #dc3545;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --text-color: #333;
    --border-radius: 8px;
    --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: var(--text-color);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.login-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* ===== رأس الصفحة ===== */
.login-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.logo {
    width: 100px;
    height: 100px;
    object-fit: contain;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.logo-text h1 {
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: 5px;
}

.logo-text p {
    color: var(--dark-color);
    font-size: 1rem;
}

/* ===== محتوى الصفحة ===== */
.login-content {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 40px;
}

.welcome-message {
    flex: 1;
    min-width: 300px;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    text-align: center;
}

.animated-text {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
    animation: fadeInUp 1s ease-out;
}

.animated-text::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
    animation: underline 3s infinite;
}

@keyframes underline {
    0% {
        width: 0;
        left: 0;
    }
    50% {
        width: 100%;
    }
    100% {
        width: 0;
        left: 100%;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.author-credit {
    font-size: 1.1rem;
    color: var(--dark-color);
    margin-bottom: 15px;
    animation: fadeIn 1.5s ease-out;
}

.app-description {
    font-size: 1.2rem;
    color: var(--secondary-color);
    font-weight: 500;
    animation: fadeIn 2s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.login-form-container {
    flex: 1;
    min-width: 300px;
}

.login-form {
    background-color: rgba(255, 255, 255, 0.9);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    animation: slideIn 1s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.login-form h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.5rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--dark-color);
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    font-family: 'Tajawal', sans-serif;
}

.form-group input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.2);
    outline: none;
}

.error-message {
    color: var(--danger-color);
    font-size: 0.85rem;
    margin-top: 5px;
    display: block;
    height: 20px;
}

.login-btn {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-family: 'Tajawal', sans-serif;
    font-weight: 500;
}

.login-btn:hover {
    background-color: #004494;
    transform: translateY(-2px);
}

.login-btn:active {
    transform: translateY(0);
}

.welcome-user {
    margin-top: 20px;
    padding: 15px;
    background-color: var(--success-color);
    color: white;
    border-radius: var(--border-radius);
    text-align: center;
    display: none;
    animation: fadeIn 0.5s ease-out;
}

.welcome-user i {
    margin-left: 10px;
    font-size: 1.2rem;
}

/* ===== ميزات التطبيق ===== */
.login-features {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 40px;
    justify-content: center;
}

.feature {
    flex: 1;
    min-width: 250px;
    max-width: 350px;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    text-align: center;
    transition: var(--transition);
}

.feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.feature i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    display: block;
}

.feature h4 {
    color: var(--dark-color);
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.feature p {
    color: #666;
    font-size: 0.95rem;
}

/* ===== تذييل الصفحة ===== */
.login-footer {
    background-color: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    text-align: center;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
}

.login-footer p {
    color: var(--dark-color);
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
}

.social-icon:hover {
    background-color: var(--dark-color);
    transform: translateY(-3px);
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 768px) {
    .login-content {
        flex-direction: column;
    }
    
    .login-features {
        flex-direction: column;
        align-items: center;
    }
    
    .feature {
        width: 100%;
    }
    
    .login-footer {
        flex-direction: column;
    }
    
    .logo-container {
        flex-direction: column;
    }
    
    .logo {
        width: 80px;
        height: 80px;
    }
    
    .logo-text h1 {
        font-size: 2rem;
    }
}
