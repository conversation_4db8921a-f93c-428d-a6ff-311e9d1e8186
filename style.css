/* style.css - تنسيقات تطبيق جمعية زواج */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'); /* إضافة Font Awesome */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
    color: #333;
    direction: rtl; /* دعم اللغة العربية */
}

/* تنسيق الهيدر الحديث */
.modern-header {
    background: linear-gradient(135deg, #0056b3 0%, #004494 100%);
    color: white;
    padding: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem 1.5rem;
}

.logo-container {
    display: flex;
    align-items: center;
    flex-direction: column;
    text-align: center;
    padding: 0.5rem 0;
}

.logo {
    background-color: white;
    color: #0056b3;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.logo i {
    font-size: 28px;
}

.modern-header h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.subtitle {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-top: 2px;
}

.main-nav {
    flex-grow: 1;
    display: flex;
    justify-content: center;
}

.main-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

.main-nav ul li {
    margin: 0 5px;
}

.main-nav ul li a {
    color: white;
    text-decoration: none;
    font-weight: 600;
    padding: 10px 15px;
    border-radius: 30px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.main-nav ul li a i {
    margin-left: 8px;
    font-size: 1.1rem;
}

.main-nav ul li a::before {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 0;
    height: 3px;
    background-color: white;
    transition: width 0.3s ease;
}

.main-nav ul li a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.main-nav ul li a:hover::before {
    width: 100%;
    right: auto;
    left: 0;
}

.main-nav ul li a.active {
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.main-nav ul li a.active::before {
    width: 100%;
}

.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.mobile-menu-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* تنسيق الهيدر للشاشات الصغيرة */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        padding: 1rem;
    }

    .logo-container {
        margin-bottom: 1rem;
    }

    .main-nav {
        width: 100%;
    }

    .main-nav ul {
        flex-direction: column;
        width: 100%;
    }

    .main-nav ul li {
        margin: 5px 0;
        width: 100%;
    }

    .main-nav ul li a {
        justify-content: center;
        padding: 12px;
        border-radius: 5px;
    }

    .mobile-menu-toggle {
        display: block;
        position: absolute;
        top: 1rem;
        left: 1rem;
    }

    .main-nav.mobile-hidden {
        display: none;
    }
}

main {
    /* إضافة ظل خفيف للمحتوى الرئيسي */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
    max-width: 1200px;
    margin: 20px auto;
    background-color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

/* إخفاء الأقسام غير النشطة */
.content-section {
    display: none;
}

/* إظهار القسم النشط */
.content-section.active {
    display: block;
}

/* تنسيق عنوان القسم الجديد */
.section-header {
    margin-bottom: 20px;
    position: relative;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
    border-right: 4px solid #0056b3;
    overflow: hidden;
}

.section-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB4PSIwIiB5PSIwIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSgxMzUpIj48cGF0aCBkPSJNMjAgMCBMMjAgNDAgTDAgMjAgTDQwIDIwIiBzdHJva2U9IiMwMDU2YjMiIHN0cm9rZS1vcGFjaXR5PSIwLjAzIiBzdHJva2Utd2lkdGg9IjEuNSIgZmlsbD0ibm9uZSIvPjwvcGF0dGVybj48L2RlZnM+PHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNwYXR0ZXJuKSIvPjwvc3ZnPg==');
    opacity: 0.5;
    z-index: 0;
}

.section-title {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
}

.section-title i {
    font-size: 1.8rem;
    color: #0056b3;
    margin-left: 12px;
    background-color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 86, 179, 0.2);
}

.section-title h2 {
    color: #0056b3;
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
    border: none;
    padding: 0;
}

.section-subtitle {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 3px;
    margin-right: 52px;
    position: relative;
    z-index: 1;
}

/* تنسيق العناوين الفرعية */
h2 {
    color: #0056b3;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

/* تنسيق حاوية الجدول */
.table-container {
    width: 100%;
    overflow-x: auto;
    margin-top: 15px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    border-radius: 6px;
}

/* تنسيق الجدول الأساسي */
.responsive-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    background-color: white;
    font-size: 0.95rem;
}

.responsive-table th,
.responsive-table td {
    border: 1px solid #e0e0e0;
    padding: 12px 15px;
    text-align: right;
    vertical-align: middle;
    white-space: nowrap;
}

.responsive-table th {
    background-color: #0056b3;
    color: white;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
    border-color: #004494;
}

.responsive-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.responsive-table tbody tr:hover {
    background-color: #e9f0f7;
}

/* تنسيق القوائم المنسدلة وحقول الإدخال داخل الجدول */
.responsive-table select,
.responsive-table input[type="date"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
    background-color: white;
}

/* تنسيق أزرار الإجراءات */
.responsive-table button {
    padding: 6px 10px;
    margin: 2px;
    cursor: pointer;
    border: none;
    border-radius: 4px;
    color: white;
    font-size: 0.85rem;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.responsive-table button i {
    margin-left: 5px;
}

.edit-btn {
    background-color: #ffc107; /* أصفر */
    color: #212529;
}

.delete-btn {
    background-color: #dc3545; /* أحمر */
}

.details-btn {
    background-color: #17a2b8; /* أزرق سماوي */
}

.edit-btn:hover { background-color: #e0a800; }
.delete-btn:hover { background-color: #c82333; }
.details-btn:hover { background-color: #138496; }

/* مؤشرات الحالة */
.table-legend {
    display: flex;
    justify-content: center;
    margin-top: 15px;
    gap: 20px;
    flex-wrap: wrap;
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.legend-item {
    display: flex;
    align-items: center;
    margin: 5px;
    padding: 5px 10px;
    border-radius: 4px;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.status-indicator {
    display: inline-block;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    margin-left: 8px;
}

/* تنسيق خلايا الحالة */
.responsive-table td.status-cell {
    font-weight: bold;
    text-align: center;
    border-radius: 4px;
}

/* أدوات البحث والتصفية */
.table-tools {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.search-box {
    display: flex;
    align-items: center;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    flex-grow: 1;
    max-width: 300px;
}

.search-box input {
    border: none;
    outline: none;
    padding: 5px;
    width: 100%;
}

.search-box i {
    color: #0056b3;
    margin-left: 5px;
}

.filter-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-options select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* تنسيق الصفوف الفارغة */
.responsive-table tr.empty-row td {
    padding: 30px;
    text-align: center;
    color: #6c757d;
    font-style: italic;
    background-color: #f8f9fa;
}

/* تنسيق قسم الإحصائيات */
.stats-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 12px;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.stats-container::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB4PSIwIiB5PSIwIiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSg0NSkiPjxjaXJjbGUgY3g9IjEwIiBjeT0iMTAiIHI9IjEuNSIgZmlsbD0iIzAwNTZiMyIgZmlsbC1vcGFjaXR5PSIwLjA1Ii8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI3BhdHRlcm4pIi8+PC9zdmc+');
    opacity: 0.8;
    z-index: 0;
}

.stat-card {
    background-color: white;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    min-width: 120px;
    text-align: center;
    flex: 1;
    max-width: 150px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 4px;
    z-index: 2;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card .stat-value {
    font-size: 1.6rem;
    font-weight: 700;
    margin: 8px 0 5px;
    position: relative;
    display: inline-block;
}

.stat-card .stat-label {
    color: #6c757d;
    font-size: 0.85rem;
    font-weight: 500;
    margin-bottom: 3px;
}

.stat-card i {
    font-size: 1.4rem;
    opacity: 0.1;
    position: absolute;
    top: 8px;
    left: 8px;
    color: #000;
}

.stat-card.primary {
    border-bottom: 3px solid #0056b3;
}

.stat-card.primary::before {
    background-color: #0056b3;
}

.stat-card.primary .stat-value {
    color: #0056b3;
}

.stat-card.success {
    border-bottom: 3px solid #28a745;
}

.stat-card.success::before {
    background-color: #28a745;
}

.stat-card.success .stat-value {
    color: #28a745;
}

.stat-card.warning {
    border-bottom: 3px solid #ffc107;
}

.stat-card.warning::before {
    background-color: #ffc107;
}

.stat-card.warning .stat-value {
    color: #ffc107;
}

.stat-card.danger {
    border-bottom: 3px solid #dc3545;
}

.stat-card.danger::before {
    background-color: #dc3545;
}

.stat-card.danger .stat-value {
    color: #dc3545;
}

/* تحسين مظهر الجدول على الشاشات الصغيرة */
@media (max-width: 768px) {
    .responsive-table th,
    .responsive-table td {
        padding: 8px;
        font-size: 0.85rem;
    }

    .responsive-table button {
        padding: 4px 8px;
        font-size: 0.75rem;
    }

    .responsive-table button i {
        margin-left: 3px;
    }

    .table-tools {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        max-width: 100%;
        margin-bottom: 10px;
    }
}

/* تنسيق ألوان حالة الانتظام */
.status-regular {
    background-color: #d4edda; /* أخضر فاتح */
    color: #155724; /* أخضر داكن */
}

.status-late {
    background-color: #fff3cd; /* برتقالي فاتح */
    color: #856404; /* برتقالي داكن */
}

.status-defaulted {
    background-color: #f8d7da; /* أحمر فاتح */
    color: #721c24; /* أحمر داكن */
}

/* تنسيق نموذج التسجيل */
.register-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

/* تنسيق رأس النموذج مع مؤشرات الخطوات */
.register-header {
    display: flex;
    justify-content: space-between;
    background-color: #f8f9fa;
    padding: 0;
    border-bottom: 1px solid #e9ecef;
}

.step-indicator {
    flex: 1;
    text-align: center;
    padding: 15px 10px;
    position: relative;
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: default;
}

.step-indicator.active {
    background-color: #e9f0f7;
    color: #0056b3;
    font-weight: bold;
}

.step-indicator.completed {
    background-color: #d4edda;
    color: #155724;
}

.step-indicator::after {
    content: '';
    position: absolute;
    top: 0;
    right: -15px;
    width: 0;
    height: 0;
    border-top: 25px solid transparent;
    border-bottom: 25px solid transparent;
    border-right: 15px solid transparent;
    border-left: 15px solid #f8f9fa;
    z-index: 1;
}

.step-indicator.active::after {
    border-left-color: #e9f0f7;
}

.step-indicator.completed::after {
    border-left-color: #d4edda;
}

.step-indicator:last-child::after {
    display: none;
}

.step-number {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50%;
    background-color: #dee2e6;
    color: #495057;
    margin-bottom: 5px;
}

.step-indicator.active .step-number {
    background-color: #0056b3;
    color: white;
}

.step-indicator.completed .step-number {
    background-color: #28a745;
    color: white;
}

/* تنسيق خطوات النموذج */
.multi-step-form {
    padding: 25px;
}

.form-step {
    display: none;
}

.form-step.active {
    display: block;
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.form-step h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #0056b3;
    font-size: 1.5rem;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

/* تنسيق مجموعات الحقول */
.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

#registerForm label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

#registerForm label i {
    margin-left: 8px;
    color: #0056b3;
    width: 20px;
    text-align: center;
}

#registerForm input[type="text"],
#registerForm input[type="tel"],
#registerForm input[type="email"],
#registerForm input[type="date"],
#registerForm select,
#registerForm textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

#registerForm input::placeholder,
#registerForm textarea::placeholder {
    color: #adb5bd;
}

#registerForm input:focus,
#registerForm select:focus,
#registerForm textarea:focus {
    border-color: #0056b3;
    box-shadow: 0 0 0 0.2rem rgba(0, 86, 179, 0.25);
    outline: none;
}

.form-hint {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 5px;
}

/* تنسيق أزرار التنقل بين الخطوات */
.form-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.next-step-btn,
.prev-step-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.next-step-btn {
    background-color: #0056b3;
    color: white;
    margin-right: auto;
}

.next-step-btn:hover {
    background-color: #004494;
}

.prev-step-btn {
    background-color: #6c757d;
    color: white;
    margin-left: auto;
}

.prev-step-btn:hover {
    background-color: #5a6268;
}

/* تنسيق زر التسجيل النهائي */
#registerForm button[type="submit"] {
    background-color: #28a745;
    color: white;
    padding: 12px 25px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: auto;
}

#registerForm button[type="submit"] i {
    margin-left: 8px;
}

#registerForm button[type="submit"]:hover {
    background-color: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#registerForm button[type="submit"]:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* تنسيق قسم الموافقة على الشروط */
.terms-agreement {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

.terms-summary {
    margin-bottom: 15px;
}

.terms-summary h4 {
    color: #0056b3;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.terms-summary ul {
    padding-right: 20px;
    margin-bottom: 10px;
}

.terms-summary li {
    margin-bottom: 5px;
    color: #495057;
}

.view-full-terms {
    display: inline-block;
    color: #0056b3;
    text-decoration: none;
    font-weight: 600;
    margin-top: 5px;
}

.view-full-terms:hover {
    text-decoration: underline;
}

.checkbox-wrapper {
    display: flex;
    align-items: flex-start;
    margin-top: 15px;
}

.checkbox-wrapper input[type="checkbox"] {
    margin-left: 10px;
    margin-top: 3px;
}

.checkbox-wrapper label {
    font-weight: 600;
    color: #495057;
}

/* تنسيق للحقول غير الصالحة */
#registerForm input:invalid,
#registerForm select:invalid,
#registerForm textarea:invalid {
    border-color: #dc3545;
}

#registerForm input:invalid:focus,
#registerForm select:invalid:focus,
#registerForm textarea:invalid:focus {
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* تنسيق للحقول الصالحة */
#registerForm input:valid:not(:placeholder-shown),
#registerForm select:valid:not([value=""]),
#registerForm textarea:valid:not(:placeholder-shown) {
    border-color: #28a745;
}

#registerForm input:valid:focus:not(:placeholder-shown),
#registerForm select:valid:focus:not([value=""]),
#registerForm textarea:valid:focus:not(:placeholder-shown) {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* تنسيق رسائل الخطأ */
.error-message {
    color: #dc3545;
    font-size: 0.85rem;
    margin-top: 5px;
    animation: fadeIn 0.3s ease-in-out;
    display: flex;
    align-items: center;
}

.error-message::before {
    content: '\f071';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-left: 5px;
    font-size: 0.9rem;
}

/* تنسيق رسائل النجاح */
.success-message {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-100px);
    background-color: #28a745;
    color: white;
    padding: 15px 25px;
    border-radius: 5px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    transition: all 0.3s ease;
}

.success-message.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}

.success-message i {
    font-size: 1.5rem;
    margin-left: 10px;
}

.success-message span {
    font-weight: 600;
}

/* تنسيق العناوين الرئيسية للأقسام */
.content-section > h2 {
    display: flex; /* استخدام flex لمحاذاة الأيقونة والنص */
    align-items: center; /* محاذاة عمودية */
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-bottom: 25px;
    color: #0056b3;
    position: relative; /* للسماح بوضع الأيقونات */
    padding-right: 40px; /* مساحة للأيقونة على اليمين */
    box-shadow: 0 2px 4px rgba(0, 86, 179, 0.1);
    background-color: #f8f9fa;
    padding: 15px 15px 15px 40px; /* تعديل الحشو لاستيعاب الأيقونة على اليمين */
    border-radius: 5px;
}

/* تعديل موضع الأيقونة لتكون على اليمين */
.content-section > h2 i {
    position: absolute;
    right: 15px; /* وضع الأيقونة على اليمين */
    left: auto; /* إلغاء الموضع الأيسر */
    top: 50%;
    transform: translateY(-50%);
    color: #007bff;
}

/* تنسيق قسم الشروط والأحكام */
#terms article {
    border: 1px solid #ddd;
    padding: 20px;
    border-radius: 8px;
    background-color: #fdfdfd;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    line-height: 1.8;
    margin-bottom: 20px;
}

#terms .agreement-controls {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border: 1px solid #eee;
}

#terms .agreement-controls label,
#terms .agreement-controls select,
#terms .agreement-controls button,
#terms .signature-controls button,
#terms .send-options button { /* تطبيق النمط على أزرار التوقيع والإرسال */
    margin-right: 10px;
    margin-bottom: 10px; /* إضافة هامش سفلي للعناصر */
}

#terms .agreement-controls select {
    padding: 8px;
    min-width: 200px; /* عرض أدنى للقائمة المنسدلة */
}

#terms .agreement-controls button,
#terms .signature-controls button,
#terms .send-options button { /* تطبيق النمط على أزرار التوقيع والإرسال */
     padding: 8px 15px;
     cursor: pointer;
     border-radius: 4px;
     border: 1px solid transparent;
}

#terms #printTermsButton {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}
#terms #printTermsButton:hover {
    background-color: #0056b3;
}

#terms #editTermsButton {
    background-color: #ffc107;
    color: #333;
    border-color: #ffc107;
}
#terms #editTermsButton:hover {
    background-color: #e0a800;
}

#terms #saveTermsButton {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
    display: none; /* إخفاء زر الحفظ مبدئيًا */
}
#terms #saveTermsButton:hover {
    background-color: #218838;
}

#terms .send-options button { /* تعديل المحدد ليكون أكثر عمومية */
    background-color: #6c757d;
    color: white;
    border-color: #6c757d;
    margin-left: 5px;
}
#terms #sendAgreementOptions button:hover {
    background-color: #5a6268;
}

#terms .signature-area {
    margin-top: 30px;
    border-top: 1px dashed #ccc;
    padding-top: 20px;
}

#terms .signature-area label {
    display: block;
    margin-bottom: 10px;
}

#terms .signature-area canvas {
    background-color: #fff; /* خلفية بيضاء للوحة التوقيع */
    border: 1px solid #000;
    cursor: crosshair;
    display: block; /* لمنع مسافة إضافية أسفل الكانفاس */
    margin-bottom: 10px;
}

#terms article[contenteditable="true"] {
    border: 2px dashed #ffc107; /* تغيير الحد ليكون أكثر وضوحًا */
    background-color: #fffacd; /* تمييز عند التعديل */
    outline: 2px dashed #ffc107;
}

/* --- تنسيقات إضافية لقسم الشروط والأحكام --- */

/* تنسيق منطقة اختيار العضو */
#memberSelectionArea {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #e9ecef;
    border-radius: 5px;
}

#memberSelectionArea label {
    margin-left: 10px;
}

/* تنسيق منطقة التوقيع */
.signature-controls {
    margin-top: 10px;
}

.signature-controls button {
    margin-left: 5px;
    background-color: #6c757d;
    color: white;
}

.signature-controls button:hover {
    background-color: #5a6268;
}

/* تنسيق خيارات الإرسال */
.send-options {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.send-options button i {
    margin-right: 5px;
}

/* تنسيق زر رفع الملف في جدول الأعضاء */
.upload-btn {
    background-color: #17a2b8; /* أزرق سماوي */
    color: white;
    padding: 5px 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 5px; /* إضافة هامش لزر الرفع */
}

.upload-btn:hover {
    background-color: #138496;
}

.upload-btn i {
    font-size: 0.9em;
}

/* تنسيق قسم الشروط والأحكام (تكملة) */
#terms article {
    line-height: 1.8;
    margin-bottom: 20px;
}

#terms h3, #terms h4 {
    color: #0056b3;
    margin-top: 20px;
}

#terms ul {
    padding-right: 20px; /* مسافة بادئة للقوائم */
}

/* تنسيق الفوتر */
footer {
    background-color: #0056b3;
    color: white;
    padding: 20px;
    margin-top: 30px;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.footer-info {
    flex: 1;
    min-width: 300px;
    text-align: right;
}

.footer-text {
    margin: 0 0 10px 0;
    font-size: 1rem;
}

.footer-contact {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.contact-item i {
    font-size: 1.1rem;
}

.footer-social {
    display: flex;
    gap: 15px;
    margin-top: 15px;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: white;
    font-size: 1.2rem;
    transition: all 0.3s;
}

.social-icon:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
}

@media (max-width: 768px) {
    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-info {
        margin-bottom: 20px;
        text-align: center;
    }

    .footer-contact {
        justify-content: center;
    }
}

/* تنسيق نافذة تفاصيل العضو */
.details-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.details-content {
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 700px;
    max-height: 85vh;
    overflow-y: auto;
    padding: 18px;
    position: relative;
}

.details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
}

.details-header h3 {
    margin: 0;
    color: #0056b3;
    font-size: 1.3rem;
}

.details-close-btn {
    background-color: transparent;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6c757d;
    transition: color 0.2s;
}

.details-close-btn:hover {
    color: #dc3545;
}

.details-section {
    margin-bottom: 18px;
}

.details-section h4 {
    color: #0056b3;
    margin-top: 0;
    margin-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 6px;
    font-size: 1.1rem;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.details-item {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.details-item-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.details-item-value {
    font-weight: 600;
    color: #212529;
}

.details-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.details-table th,
.details-table td {
    padding: 10px;
    text-align: right;
    border: 1px solid #dee2e6;
}

.details-table th {
    background-color: #e9ecef;
    font-weight: 600;
}

.details-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.details-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 20px;
}

.details-stat-card {
    flex: 1;
    min-width: 150px;
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.details-stat-card.primary {
    border-top: 3px solid #0056b3;
}

.details-stat-card.success {
    border-top: 3px solid #28a745;
}

.details-stat-card.warning {
    border-top: 3px solid #ffc107;
}

.details-stat-card.danger {
    border-top: 3px solid #dc3545;
}

.details-stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #212529;
    margin: 5px 0;
}

.details-stat-label {
    font-size: 0.85rem;
    color: #6c757d;
}

.details-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.details-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s;
}

.details-btn-primary {
    background-color: #0056b3;
    color: white;
}

.details-btn-primary:hover {
    background-color: #004494;
}

.details-btn-secondary {
    background-color: #6c757d;
    color: white;
}

.details-btn-secondary:hover {
    background-color: #5a6268;
}

.details-btn-danger {
    background-color: #dc3545;
    color: white;
}

.details-btn-danger:hover {
    background-color: #c82333;
}

.details-btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.details-btn-warning:hover {
    background-color: #e0a800;
}

.details-form-group {
    margin-bottom: 15px;
}

.details-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.details-form-group input,
.details-form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* تنسيقات الطباعة */
/* تنسيق النافذة المنبثقة لنسخ الأسماء المتعددة */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    overflow: auto;
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    width: 80%;
    max-width: 700px;
    direction: rtl;
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {opacity: 0; transform: translateY(-30px);}
    to {opacity: 1; transform: translateY(0);}
}

.modal-header {
    padding: 15px 20px;
    background-color: #0056b3;
    color: white;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.4rem;
}

.close-modal {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-modal:hover {
    color: #f8f9fa;
}

.modal-body {
    padding: 20px;
}

.modal-body p {
    margin-top: 0;
    margin-bottom: 10px;
}

.modal-body textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-family: inherit;
    font-size: 1rem;
    resize: vertical;
}

.names-preview {
    margin-top: 20px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    background-color: #f8f9fa;
}

.names-preview h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #0056b3;
    font-size: 1.1rem;
}

.names-preview ul {
    margin: 0;
    padding-right: 20px;
}

.names-preview li {
    margin-bottom: 5px;
}

.modal-footer {
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 8px 8px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn-primary {
    background-color: #0056b3;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.2s;
}

.btn-primary:hover {
    background-color: #004494;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.2s;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.paste-names-btn {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.2s;
    white-space: nowrap;
}

.paste-names-btn:hover {
    background-color: #218838;
}

/* تنسيق علامات التبويب في النافذة المنبثقة */
.modal-tabs {
    display: flex;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 20px;
}

.tab-btn {
    padding: 10px 15px;
    background-color: transparent;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 600;
    color: #6c757d;
    transition: all 0.2s;
}

.tab-btn:hover {
    color: #0056b3;
}

.tab-btn.active {
    color: #0056b3;
    border-bottom-color: #0056b3;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* تنسيق جدول الأسماء القابل للتعديل */
.names-table-container {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 15px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.editable-names-table {
    width: 100%;
    border-collapse: collapse;
}

.editable-names-table th,
.editable-names-table td {
    padding: 10px;
    text-align: right;
    border-bottom: 1px solid #dee2e6;
}

.editable-names-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.editable-names-table tr:hover {
    background-color: #f8f9fa;
}

.editable-names-table input[type="text"] {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-family: inherit;
    font-size: 1rem;
}

.editable-names-table input[type="text"]:focus {
    border-color: #0056b3;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 86, 179, 0.25);
}

.name-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.name-actions button {
    border: none;
    background-color: transparent;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s;
}

.name-actions button:hover {
    background-color: #f8f9fa;
}

.name-actions button.edit-btn:hover {
    color: #0056b3;
}

.name-actions button.delete-btn:hover {
    color: #dc3545;
}

.bulk-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.btn-success {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.2s;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.2s;
}

.btn-danger:hover {
    background-color: #c82333;
}

.text-center {
    text-align: center;
    margin-top: 15px;
}

/* تنسيقات قسم التحصيل المالي */
.section-header {
    margin-bottom: 25px;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 15px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}

.section-title i {
    font-size: 1.8rem;
    color: #0056b3;
}

.section-title h2 {
    margin: 0;
    font-size: 1.8rem;
}

.section-subtitle {
    color: #6c757d;
    font-size: 1rem;
}

/* تنسيقات قسم المتزوجين وتواريخ الزواج */
.marriage-summary-container {
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    overflow: hidden;
}

.marriage-summary-header {
    background-color: #0056b3;
    color: white;
    padding: 15px 20px;
}

.marriage-summary-header h3 {
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.marriage-tabs {
    display: flex;
    background-color: #e9ecef;
    border-bottom: 1px solid #dee2e6;
}

.marriage-tab {
    padding: 12px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 600;
    color: #495057;
    position: relative;
    transition: all 0.2s;
}

.marriage-tab:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.marriage-tab.active {
    color: #0056b3;
    background-color: white;
}

.marriage-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 3px;
    background-color: #0056b3;
}

.marriage-tab-content {
    display: none;
    padding: 20px;
}

.marriage-tab-content.active {
    display: block;
}

.marriage-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.marriage-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 15px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.marriage-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.marriage-card-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 10px;
}

.marriage-card-name {
    font-weight: 600;
    font-size: 1.1rem;
    margin: 0;
    color: #0056b3;
}

.marriage-card-date {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 10px;
}

.marriage-card-date i {
    color: #28a745;
}

.marriage-card-countdown {
    background-color: #e9ecef;
    border-radius: 4px;
    padding: 8px 12px;
    margin-top: 10px;
    font-size: 0.9rem;
    color: #495057;
}

.marriage-card-countdown.urgent {
    background-color: #ffc107;
    color: #212529;
}

.marriage-card-countdown.very-soon {
    background-color: #dc3545;
    color: white;
}

.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 30px;
    color: #6c757d;
}

.empty-state i {
    font-size: 2rem;
    margin-bottom: 10px;
}

/* تنسيقات نموذج التحصيل */
.collection-form-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    overflow: hidden;
}

.collection-form-header {
    background-color: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
}

.collection-form-header h3 {
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #212529;
}

.collection-form-header h3 i {
    color: #28a745;
}

.collection-selection-type {
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.selection-type-label {
    font-weight: 600;
    margin-bottom: 10px;
    color: #495057;
}

.selection-type-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.selection-option {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px 15px;
    border-radius: 4px;
    background-color: white;
    border: 1px solid #ced4da;
    transition: all 0.2s;
}

.selection-option:hover {
    border-color: #0056b3;
}

.selection-option input[type="radio"] {
    margin: 0;
}

.selection-option input[type="radio"]:checked + .option-text {
    color: #0056b3;
    font-weight: 600;
}

.option-text {
    display: flex;
    align-items: center;
    gap: 8px;
}

.collection-form {
    padding: 20px;
}

.member-selection-container {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.members-multi-select-container {
    border: 1px solid #ced4da;
    border-radius: 4px;
    overflow: hidden;
}

.members-search {
    position: relative;
    padding: 10px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.members-search input {
    width: 100%;
    padding: 8px 35px 8px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.members-search i {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.members-list {
    max-height: 200px;
    overflow-y: auto;
    padding: 10px;
}

.member-checkbox-item {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    border-bottom: 1px solid #f0f0f0;
}

.member-checkbox-item:last-child {
    border-bottom: none;
}

.member-checkbox-item label {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
    cursor: pointer;
}

.member-checkbox-item input[type="checkbox"] {
    margin: 0;
}

.members-selection-controls {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.members-selection-controls button {
    background: none;
    border: none;
    color: #0056b3;
    cursor: pointer;
    padding: 5px 10px;
    font-size: 0.9rem;
}

.members-selection-controls button:hover {
    text-decoration: underline;
}

.selected-members-preview {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.preview-header {
    margin-bottom: 10px;
}

.preview-header h4 {
    margin: 0;
    font-size: 1.1rem;
    color: #495057;
}

.selected-members-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.selected-member-tag {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #e9ecef;
    border-radius: 20px;
    padding: 5px 12px;
    font-size: 0.9rem;
}

.selected-member-tag button {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    font-size: 0.8rem;
    padding: 0;
    display: flex;
    align-items: center;
}

.collection-details {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 15px;
}

.form-row .form-group {
    flex: 1;
    min-width: 200px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

/* تنسيقات جدول التحصيلات */
.collection-table-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.collection-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.collection-table-header h3 {
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #212529;
}

.collection-table-header h3 i {
    color: #0056b3;
}

.table-actions {
    display: flex;
    gap: 10px;
}

.table-filters {
    display: flex;
    justify-content: space-between;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.search-box {
    position: relative;
    width: 300px;
}

.search-box input {
    width: 100%;
    padding: 8px 35px 8px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.search-box i {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.filter-options {
    display: flex;
    gap: 10px;
}

.filter-options select {
    padding: 8px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: white;
}

.table-responsive {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

.data-table .action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.data-table .action-buttons button {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s;
}

.data-table .action-buttons button:hover {
    background-color: #f0f0f0;
}

.data-table .action-buttons button.edit-btn:hover {
    color: #0056b3;
}

.data-table .action-buttons button.delete-btn:hover {
    color: #dc3545;
}

.collection-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    padding: 20px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.summary-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    padding: 15px;
    text-align: center;
}

.summary-title {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.summary-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #0056b3;
}

@media print {
    body {
        background-color: white;
        font-size: 12pt;
    }
    header, nav, footer, #registerForm button, #members button, #printTermsButton, #agreeTerms, label[for="agreeTerms"], .details-modal, .modal {
        display: none; /* إخفاء العناصر غير المرغوبة عند الطباعة */
    }
    main {
    /* إضافة ظل خفيف للمحتوى الرئيسي */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        box-shadow: none;
        margin: 0;
        max-width: 100%;
        padding: 0;
    }
    table th, table td {
        border: 1px solid #000; /* حدود أوضح للطباعة */
    }
    a {
        text-decoration: none;
        color: black;
    }
    /* إظهار القسم المطلوب طباعته فقط (إذا لزم الأمر) */
    /* .content-section { display: none; } */
    /* #terms { display: block !important; } */ /* مثال لطباعة الشروط فقط */
}